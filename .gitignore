# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
fin_mcp_server
fin_mcp_server.exe

# Test binary, built with `go test -c`
*.test
build

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# PM2 logs and runtime files
logs/
*.log
.pm2/
pids/
*.pid
*.seed
*.pid.lock

# PM2 ecosystem file with sensitive data (keep template)
# ecosystem.config.js

# Database files
*.db
*.sqlite
*.sqlite3

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Build artifacts
dist/
build/
out/

# Coverage reports
coverage.txt
coverage.html
coverage.out

# Backup files
*.bak
*.backup

# Local configuration files
config.local.*
*.local

# Docker files (if any remain)
.dockerignore
Dockerfile*
docker-compose*.yml

# Node modules (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Archive files
*.tar.gz
*.zip
*.rar
