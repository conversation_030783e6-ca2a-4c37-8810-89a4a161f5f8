package main

import (
	"context"
	"log"
	"os"

	"fin_mcp_server/internal/db"
	"fin_mcp_server/internal/mcp"
)

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func main() {
	// Get PostgreSQL configuration from environment variables
	host := getEnv("POSTGRES_HOST", "localhost")
	port := getEnv("POSTGRES_PORT", "5432")
	user := getEnv("POSTGRES_USER", "postgres")
	password := getEnv("POSTGRES_PASSWORD", "postgres")
	dbname := getEnv("POSTGRES_DB", "postgres")
	schema := getEnv("POSTGRES_SCHEMA", "public")

	log.Printf("Connecting to PostgreSQL: %s@%s:%s/%s (schema: %s)", user, host, port, dbname, schema)

	pg, err := db.NewPostgres(host, port, user, password, dbname, schema)
	if err != nil {
		log.Fatalf("Database connection failed: %v", err)
	}

	log.Printf("Successfully connected to PostgreSQL database")

	// Test database connectivity
	if err := pg.DB.Ping(); err != nil {
		log.Fatalf("Database ping failed: %v", err)
	}

	server := &mcp.MCPServer{DB: pg.DB, Schema: pg.Schema}
	if err := server.Start(context.Background()); err != nil {
		log.Fatal("server error:", err)
	}
}
