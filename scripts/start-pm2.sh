#!/bin/bash

# Simple script to start MCP Server with PM2

echo "Starting PostgreSQL MCP Server with PM2..."

# Load environment variables
if [ -f .env ]; then
    echo "Loading environment variables from .env file..."
    export $(cat .env | grep -v '^#' | grep -v '^$' | xargs)

    # Display loaded environment variables dynamically
    echo ""
    echo "Loaded Environment Variables:"
    echo "=============================="
    while IFS='=' read -r key value; do
        # Skip comments and empty lines
        if [[ ! "$key" =~ ^#.*$ ]] && [[ -n "$key" ]]; then
            # Remove any quotes from the value for display
            clean_value=$(echo "$value" | sed 's/^["'\'']*//;s/["'\'']*$//')
            echo "  $key: $clean_value"
        fi
    done < .env
    echo ""
else
    echo "Warning: .env file not found!"
    exit 1
fi

# Build application
echo "Building application..."
go build -o fin_mcp_server main.go
if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

# Create logs directory
mkdir -p logs

# Start PM2 services
echo "Starting PM2 services..."
pm2 start ecosystem.config.js

echo ""
echo "PM2 services started successfully!"
echo ""
echo "Available commands:"
echo "  pm2 status    - Show service status"
echo "  pm2 logs      - Show logs"
echo "  pm2 stop all  - Stop all services"
echo "  pm2 restart all - Restart all services"
echo ""
echo "Server endpoints (based on current configuration):"
if [ "$MCP_TRANSPORT" = "streamable-http" ] || [ "$MCP_TRANSPORT" = "sse" ]; then
    echo "  HTTP: http://localhost:${MCP_PORT:-8080}/mcp"
    echo "  Health: http://localhost:${MCP_PORT:-8080}/health"
    if [ "$MCP_TRANSPORT" = "sse" ]; then
        echo "  SSE: http://localhost:${MCP_PORT:-8081}"
    fi
else
    echo "  STDIO mode - no HTTP endpoints available"
fi
echo ""
