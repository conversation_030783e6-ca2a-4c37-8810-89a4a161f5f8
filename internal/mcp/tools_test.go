package mcp

import (
	"testing"
)

func TestValidateQuery(t *testing.T) {
	tools := &Tools{}

	// Test valid SELECT queries
	validQueries := []string{
		"SELECT * FROM users",
		"select name, email from customers",
		"SELECT u.name, p.title FROM users u JOIN posts p ON u.id = p.user_id",
		"  SELECT * FROM products WHERE price > 100 LIMIT 10  ",
		"SELECT COUNT(*) FROM orders",
		"SELECT category, AVG(price) FROM products GROUP BY category",
	}

	for _, query := range validQueries {
		err := tools.validateQuery(query)
		if err != nil {
			t.Errorf("Expected valid query '%s' to pass validation, but got error: %v", query, err)
		}
	}

	// Test invalid queries (should be rejected)
	invalidQueries := []string{
		"INSERT INTO users (name) VALUES ('test')",
		"UPDATE users SET name = 'test'",
		"DELETE FROM users",
		"DROP TABLE users",
		"CREATE TABLE test (id INT)",
		"ALTER TABLE users ADD COLUMN test VARCHAR(255)",
		"TRUNCATE TABLE users",
		"GRANT ALL ON users TO public",
		"REVOKE ALL ON users FROM public",
		"EXEC sp_test",
		"EXECUTE sp_test",
		"SELECT * FROM users; DROP TABLE users;",
	}

	for _, query := range invalidQueries {
		err := tools.validateQuery(query)
		if err == nil {
			t.Errorf("Expected invalid query '%s' to fail validation, but it passed", query)
		}
	}
}

func TestValidateQueryEdgeCases(t *testing.T) {
	tools := &Tools{}

	// Test queries with keywords in column names or table names (should be allowed)
	edgeCases := []string{
		"SELECT insert_date FROM logs",           // 'insert' as column name
		"SELECT * FROM update_log",               // 'update' in table name
		"SELECT description FROM delete_history", // 'delete' in table name
	}

	for _, query := range edgeCases {
		err := tools.validateQuery(query)
		if err != nil {
			t.Errorf("Expected edge case query '%s' to pass validation, but got error: %v", query, err)
		}
	}
}

func TestQuerySimplification(t *testing.T) {
	// Test that demonstrates the simplified approach
	testCases := []struct {
		name        string
		query       string
		shouldError bool
		description string
	}{
		{
			name:        "Simple table query",
			query:       "SELECT * FROM users LIMIT 10",
			shouldError: false,
			description: "Replaces old table + limit parameters",
		},
		{
			name:        "Complex query with JOIN",
			query:       "SELECT u.name, COUNT(o.id) as order_count FROM users u LEFT JOIN orders o ON u.id = o.user_id GROUP BY u.id ORDER BY order_count DESC LIMIT 5",
			shouldError: false,
			description: "Shows power of direct SQL approach",
		},
		{
			name:        "Query with WHERE clause",
			query:       "SELECT name, email FROM customers WHERE country = 'Indonesia' AND active = true ORDER BY created_at DESC",
			shouldError: false,
			description: "More flexible than old table-only approach",
		},
		{
			name:        "Invalid query",
			query:       "DROP TABLE users",
			shouldError: true,
			description: "Security validation still works",
		},
	}

	tools := &Tools{}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := tools.validateQuery(tc.query)
			if tc.shouldError && err == nil {
				t.Errorf("Expected query '%s' to fail validation", tc.query)
			}
			if !tc.shouldError && err != nil {
				t.Errorf("Expected query '%s' to pass validation, got error: %v", tc.query, err)
			}
		})
	}
}
