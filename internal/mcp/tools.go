package mcp

import (
	"context"
	"database/sql"
	"fmt"
	"regexp"
	"strings"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
)

type Tools struct {
	DB     *sql.DB
	Schema string
}

// formatTableName formats table/view name with schema prefix if schema is specified
func (t *Tools) formatTableName(name string) string {
	if t.Schema != "" && t.Schema != "public" {
		return fmt.Sprintf("%s.%s", t.Schema, name)
	}
	return name
}

// validateQuery validates that the query is safe to execute (only SELECT statements)
func (t *Tools) validateQuery(query string) error {
	// Remove leading/trailing whitespace and convert to lowercase for validation
	trimmedQuery := strings.TrimSpace(strings.ToLower(query))

	// Check if query starts with SELECT
	if !strings.HasPrefix(trimmedQuery, "select") {
		return fmt.Errorf("only SELECT queries are allowed")
	}

	// Check for potentially dangerous keywords
	dangerousKeywords := []string{
		"insert", "update", "delete", "drop", "create", "alter",
		"truncate", "grant", "revoke", "exec", "execute", "xp_",
	}

	for _, keyword := range dangerousKeywords {
		// Use word boundaries to avoid false positives
		pattern := fmt.Sprintf(`\b%s\b`, regexp.QuoteMeta(keyword))
		matched, _ := regexp.MatchString(pattern, trimmedQuery)
		if matched {
			return fmt.Errorf("query contains forbidden keyword: %s", keyword)
		}
	}

	return nil
}

// executeQuery executes a SQL query and returns the results in JSON format
func (t *Tools) executeQuery(ctx context.Context, query string) (*mcp.CallToolResult, error) {
	// Validate query first
	if err := t.validateQuery(query); err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("query validation failed: %v", err)), nil
	}

	rows, err := t.DB.QueryContext(ctx, query)
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("database error: %v", err)), nil
	}
	defer rows.Close()

	cols, _ := rows.Columns()
	results := []map[string]any{}

	for rows.Next() {
		colsData := make([]any, len(cols))
		colsPtr := make([]any, len(cols))
		for i := range cols {
			colsPtr[i] = &colsData[i]
		}
		if err := rows.Scan(colsPtr...); err != nil {
			return mcp.NewToolResultError(fmt.Sprintf("scan error: %v", err)), nil
		}
		rowMap := map[string]any{}
		for i, col := range cols {
			val := colsPtr[i].(*any)
			rowMap[col] = *val
		}
		results = append(results, rowMap)
	}

	result, err := mcp.NewToolResultJSON(map[string]any{"rows": results})
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("failed to create JSON result: %v", err)), nil
	}
	return result, nil
}

// Handler untuk query unified - menggantikan query_table dan query_view
func (t *Tools) Query(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	// Prioritas 1: Jika ada parameter 'query', gunakan query SQL langsung
	if queryStr := request.GetString("query", ""); queryStr != "" {
		return t.executeQuery(ctx, queryStr)
	}

	// Prioritas 2: Backward compatibility - parameter 'table'
	if table := request.GetString("table", ""); table != "" {
		limit := request.GetString("limit", "10")
		fullTableName := t.formatTableName(table)
		query := fmt.Sprintf("SELECT * FROM %s LIMIT %s", fullTableName, limit)
		return t.executeQuery(ctx, query)
	}

	// Prioritas 3: Backward compatibility - parameter 'view'
	if view := request.GetString("view", ""); view != "" {
		limit := request.GetString("limit", "10")
		fullViewName := t.formatTableName(view)
		query := fmt.Sprintf("SELECT * FROM %s LIMIT %s", fullViewName, limit)
		return t.executeQuery(ctx, query)
	}

	return mcp.NewToolResultError("missing required parameter: 'query', 'table', or 'view'"), nil
}

// Handler untuk list tables
func (t *Tools) ListTables(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	schema := t.Schema
	if schema == "" {
		schema = "public"
	}

	q := `SELECT table_name, table_type
		  FROM information_schema.tables
		  WHERE table_schema = $1
		  ORDER BY table_name`

	rows, err := t.DB.QueryContext(ctx, q, schema)
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("database error: %v", err)), nil
	}
	defer rows.Close()

	results := []map[string]any{}
	for rows.Next() {
		var tableName, tableType string
		if err := rows.Scan(&tableName, &tableType); err != nil {
			return mcp.NewToolResultError(fmt.Sprintf("scan error: %v", err)), nil
		}
		results = append(results, map[string]any{
			"table_name": tableName,
			"table_type": tableType,
		})
	}

	result, err := mcp.NewToolResultJSON(map[string]any{
		"schema": schema,
		"tables": results,
	})
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("failed to create JSON result: %v", err)), nil
	}
	return result, nil
}

// Handler untuk list views
func (t *Tools) ListViews(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	schema := t.Schema
	if schema == "" {
		schema = "public"
	}

	q := `SELECT table_name as view_name, view_definition
		  FROM information_schema.views
		  WHERE table_schema = $1
		  ORDER BY table_name`

	rows, err := t.DB.QueryContext(ctx, q, schema)
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("database error: %v", err)), nil
	}
	defer rows.Close()

	results := []map[string]any{}
	for rows.Next() {
		var viewName, viewDefinition string
		if err := rows.Scan(&viewName, &viewDefinition); err != nil {
			return mcp.NewToolResultError(fmt.Sprintf("scan error: %v", err)), nil
		}
		results = append(results, map[string]any{
			"view_name":       viewName,
			"view_definition": viewDefinition,
		})
	}

	result, err := mcp.NewToolResultJSON(map[string]any{
		"schema": schema,
		"views":  results,
	})
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("failed to create JSON result: %v", err)), nil
	}
	return result, nil
}

// Handler untuk describe table
func (t *Tools) DescribeTable(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	table, err := request.RequireString("table")
	if err != nil {
		return mcp.NewToolResultError("missing table parameter"), nil
	}

	schema := t.Schema
	if schema == "" {
		schema = "public"
	}

	q := `SELECT
			column_name,
			data_type,
			is_nullable,
			column_default,
			character_maximum_length,
			numeric_precision,
			numeric_scale,
			ordinal_position
		  FROM information_schema.columns
		  WHERE table_name = $1
		  AND table_schema = $2
		  ORDER BY ordinal_position`

	rows, err := t.DB.QueryContext(ctx, q, table, schema)
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("database error: %v", err)), nil
	}
	defer rows.Close()

	results := []map[string]any{}
	for rows.Next() {
		var columnName, dataType, isNullable string
		var columnDefault, charMaxLength, numericPrecision, numericScale, ordinalPosition any

		if err := rows.Scan(&columnName, &dataType, &isNullable, &columnDefault,
			&charMaxLength, &numericPrecision, &numericScale, &ordinalPosition); err != nil {
			return mcp.NewToolResultError(fmt.Sprintf("scan error: %v", err)), nil
		}

		results = append(results, map[string]any{
			"column_name":              columnName,
			"data_type":                dataType,
			"is_nullable":              isNullable,
			"column_default":           columnDefault,
			"character_maximum_length": charMaxLength,
			"numeric_precision":        numericPrecision,
			"numeric_scale":            numericScale,
			"ordinal_position":         ordinalPosition,
		})
	}

	result, err := mcp.NewToolResultJSON(map[string]any{
		"schema":  schema,
		"table":   table,
		"columns": results,
	})
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("failed to create JSON result: %v", err)), nil
	}
	return result, nil
}

// Handler untuk list schemas
func (t *Tools) ListSchemas(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	q := `SELECT schema_name
		  FROM information_schema.schemata
		  WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
		  ORDER BY schema_name`

	rows, err := t.DB.QueryContext(ctx, q)
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("database error: %v", err)), nil
	}
	defer rows.Close()

	results := []map[string]any{}
	for rows.Next() {
		var schemaName string
		if err := rows.Scan(&schemaName); err != nil {
			return mcp.NewToolResultError(fmt.Sprintf("scan error: %v", err)), nil
		}
		results = append(results, map[string]any{
			"schema_name": schemaName,
		})
	}

	result, err := mcp.NewToolResultJSON(map[string]any{
		"schemas": results,
	})
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("failed to create JSON result: %v", err)), nil
	}
	return result, nil
}

// Handler untuk get table constraints
func (t *Tools) GetTableConstraints(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	table, err := request.RequireString("table")
	if err != nil {
		return mcp.NewToolResultError("missing table parameter"), nil
	}

	schema := t.Schema
	if schema == "" {
		schema = "public"
	}

	q := `SELECT
			tc.constraint_name,
			tc.constraint_type,
			kcu.column_name,
			tc.is_deferrable,
			tc.initially_deferred
		  FROM information_schema.table_constraints tc
		  LEFT JOIN information_schema.key_column_usage kcu
			ON tc.constraint_name = kcu.constraint_name
			AND tc.table_schema = kcu.table_schema
		  WHERE tc.table_name = $1
		  AND tc.table_schema = $2
		  ORDER BY tc.constraint_type, tc.constraint_name, kcu.ordinal_position`

	rows, err := t.DB.QueryContext(ctx, q, table, schema)
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("database error: %v", err)), nil
	}
	defer rows.Close()

	results := []map[string]any{}
	for rows.Next() {
		var constraintName, constraintType string
		var columnName, isDeferrable, initiallyDeferred any

		if err := rows.Scan(&constraintName, &constraintType, &columnName,
			&isDeferrable, &initiallyDeferred); err != nil {
			return mcp.NewToolResultError(fmt.Sprintf("scan error: %v", err)), nil
		}

		results = append(results, map[string]any{
			"constraint_name":    constraintName,
			"constraint_type":    constraintType,
			"column_name":        columnName,
			"is_deferrable":      isDeferrable,
			"initially_deferred": initiallyDeferred,
		})
	}

	result, err := mcp.NewToolResultJSON(map[string]any{
		"schema":      schema,
		"table":       table,
		"constraints": results,
	})
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("failed to create JSON result: %v", err)), nil
	}
	return result, nil
}

// Handler untuk get view definition
func (t *Tools) GetViewDefinition(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	view, err := request.RequireString("view")
	if err != nil {
		return mcp.NewToolResultError("missing view parameter"), nil
	}

	schema := t.Schema
	if schema == "" {
		schema = "public"
	}

	q := `SELECT view_definition, check_option, is_updatable, is_insertable_into
		  FROM information_schema.views
		  WHERE table_name = $1
		  AND table_schema = $2`

	rows, err := t.DB.QueryContext(ctx, q, view, schema)
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("database error: %v", err)), nil
	}
	defer rows.Close()

	if !rows.Next() {
		return mcp.NewToolResultError(fmt.Sprintf("view '%s' not found in schema '%s'", view, schema)), nil
	}

	var viewDefinition, checkOption, isUpdatable, isInsertableInto string
	if err := rows.Scan(&viewDefinition, &checkOption, &isUpdatable, &isInsertableInto); err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("scan error: %v", err)), nil
	}

	result, err := mcp.NewToolResultJSON(map[string]any{
		"schema":             schema,
		"view":               view,
		"view_definition":    viewDefinition,
		"check_option":       checkOption,
		"is_updatable":       isUpdatable,
		"is_insertable_into": isInsertableInto,
	})
	if err != nil {
		return mcp.NewToolResultError(fmt.Sprintf("failed to create JSON result: %v", err)), nil
	}
	return result, nil
}

// Register semua tools ke server
func (t *Tools) Register(srv *server.MCPServer) {
	// Tool untuk query unified - menggantikan query_table dan query_view
	queryTool := mcp.NewTool("query",
		mcp.WithDescription("Execute SQL SELECT queries on PostgreSQL tables and views. Supports direct SQL queries or simple table/view queries with limit. For security, only SELECT statements are allowed."),
		mcp.WithString("query"),
		mcp.WithString("table"),
		mcp.WithString("view"),
		mcp.WithString("limit"),
	)
	srv.AddTool(queryTool, t.Query)

	// Tool untuk list tables
	listTablesTool := mcp.NewTool("list_tables",
		mcp.WithDescription("List semua tabel dalam schema PostgreSQL"),
	)
	srv.AddTool(listTablesTool, t.ListTables)

	// Tool untuk list views
	listViewsTool := mcp.NewTool("list_views",
		mcp.WithDescription("List semua view dalam schema PostgreSQL"),
	)
	srv.AddTool(listViewsTool, t.ListViews)

	// Tool untuk describe table
	describeTableTool := mcp.NewTool("describe_table",
		mcp.WithDescription("Mendapatkan struktur detail dari tabel atau view PostgreSQL"),
		mcp.WithString("table", mcp.Required()),
	)
	srv.AddTool(describeTableTool, t.DescribeTable)

	// Tool untuk list schemas
	listSchemasTool := mcp.NewTool("list_schemas",
		mcp.WithDescription("List semua schema dalam database PostgreSQL"),
	)
	srv.AddTool(listSchemasTool, t.ListSchemas)

	// Tool untuk get table constraints
	getTableConstraintsTool := mcp.NewTool("get_table_constraints",
		mcp.WithDescription("Mendapatkan semua constraint (primary key, foreign key, unique, check) dari tabel PostgreSQL"),
		mcp.WithString("table", mcp.Required()),
	)
	srv.AddTool(getTableConstraintsTool, t.GetTableConstraints)

	// Tool untuk get view definition
	getViewDefinitionTool := mcp.NewTool("get_view_definition",
		mcp.WithDescription("Mendapatkan definisi SQL lengkap dari view PostgreSQL"),
		mcp.WithString("view", mcp.Required()),
	)
	srv.AddTool(getViewDefinitionTool, t.GetViewDefinition)
}
