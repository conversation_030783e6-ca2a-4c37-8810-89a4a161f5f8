package mcp

import (
	"context"
	"database/sql"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/mark3labs/mcp-go/server"
)

type MCPServer struct {
	DB     *sql.DB
	Schema string
}

type TransportType string

const (
	TransportSTDIO          TransportType = "stdio"
	TransportStreamableHTTP TransportType = "streamable-http"
	TransportSSE            TransportType = "sse"
)

func (s *MCPServer) Start(ctx context.Context) error {
	srv := server.NewMCPServer("pg-mcp", "0.1.0")

	tools := &Tools{DB: s.DB, Schema: s.Schema}
	tools.Register(srv)

	// Determine transport type from environment variable
	transportType := TransportType(os.Getenv("MCP_TRANSPORT"))
	if transportType == "" {
		transportType = TransportSTDIO // default to STDIO
	}

	port := os.Getenv("MCP_PORT")
	if port == "" {
		port = "8080" // default port
	}

	switch transportType {
	case TransportStreamableHTTP:
		return s.startHTTPServer(srv, port)

	case TransportSSE:
		return s.startSSEServer(srv, port)

	default: // TransportSTDIO
		log.Println("MCP STDIO server started")
		return server.ServeStdio(srv)
	}
}

func (s *MCPServer) startHTTPServer(srv *server.MCPServer, port string) error {
	log.Printf("MCP StreamableHTTP server starting on port %s", port)

	// Create HTTP server with health check
	httpServer := server.NewStreamableHTTPServer(srv)

	// Add health check endpoint
	mux := http.NewServeMux()
	mux.Handle("/mcp", httpServer)
	mux.HandleFunc("/health", s.healthCheckHandler)
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"service":"PostgreSQL MCP Server","status":"running","transport":"streamable-http","endpoints":["/mcp","/health"]}`))
	})

	// Create HTTP server
	httpSrv := &http.Server{
		Addr:         ":" + port,
		Handler:      mux,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  120 * time.Second,
	}

	// Setup graceful shutdown
	go func() {
		sigChan := make(chan os.Signal, 1)
		signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
		<-sigChan

		log.Println("Shutting down HTTP server...")
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		if err := httpSrv.Shutdown(ctx); err != nil {
			log.Printf("HTTP server shutdown error: %v", err)
		}
	}()

	log.Printf("Server running at http://localhost:%s", port)
	log.Printf("MCP endpoint: http://localhost:%s/mcp", port)
	log.Printf("Health check: http://localhost:%s/health", port)

	return httpSrv.ListenAndServe()
}

func (s *MCPServer) startSSEServer(srv *server.MCPServer, port string) error {
	log.Printf("MCP SSE server starting on port %s", port)

	sseServer := server.NewSSEServer(srv)

	// Setup graceful shutdown
	go func() {
		sigChan := make(chan os.Signal, 1)
		signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
		<-sigChan

		log.Println("Shutting down SSE server...")
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		if err := sseServer.Shutdown(ctx); err != nil {
			log.Printf("SSE server shutdown error: %v", err)
		}
	}()

	log.Printf("SSE server running at http://localhost:%s", port)
	return sseServer.Start(":" + port)
}

func (s *MCPServer) healthCheckHandler(w http.ResponseWriter, r *http.Request) {
	// Test database connection
	if err := s.DB.Ping(); err != nil {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusServiceUnavailable)
		w.Write([]byte(`{"status":"unhealthy","error":"database connection failed"}`))
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(`{"status":"healthy","database":"connected","timestamp":"` + time.Now().UTC().Format(time.RFC3339) + `"}`))
}
