package db

import (
	"database/sql"
	"fmt"

	_ "github.com/lib/pq"
)

type Postgres struct {
	DB     *sql.DB
	Schema string
}

func NewPostgres(host, port, user, password, dbname, schema string) (*Postgres, error) {
	connStr := fmt.Sprintf(
		"host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		host, port, user, password, dbname,
	)

	db, err := sql.Open("postgres", connStr)
	if err != nil {
		return nil, fmt.Errorf("failed to open database connection: %v", err)
	}

	// Test the connection
	if err := db.Ping(); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to connect to PostgreSQL database '%s' on %s:%s - please ensure the database exists and is accessible: %v", dbname, host, port, err)
	}

	// Set search_path to the specified schema if provided
	if schema != "" && schema != "public" {
		_, err = db.Exec(fmt.Sprintf("SET search_path TO %s, public", schema))
		if err != nil {
			db.Close()
			return nil, fmt.<PERSON><PERSON><PERSON>("failed to set search_path to schema '%s' - please ensure the schema exists: %v", schema, err)
		}
	}

	return &Postgres{DB: db, Schema: schema}, nil
}
