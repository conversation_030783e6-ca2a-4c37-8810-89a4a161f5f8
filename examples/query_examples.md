# Query Tool Examples

This document shows examples of how to use the simplified `query` tool that provides maximum flexibility for AI agents.

## SQL Queries (Only Parameter)

### Basic SELECT
```json
{
  "name": "query",
  "arguments": {
    "query": "SELECT * FROM users LIMIT 5"
  }
}
```

### SELECT with WHERE clause
```json
{
  "name": "query",
  "arguments": {
    "query": "SELECT name, email FROM users WHERE active = true ORDER BY created_at DESC LIMIT 10"
  }
}
```

### JOIN queries
```json
{
  "name": "query",
  "arguments": {
    "query": "SELECT u.name, p.title, p.created_at FROM users u JOIN posts p ON u.id = p.user_id WHERE p.published = true LIMIT 10"
  }
}
```

### Aggregate functions
```json
{
  "name": "query",
  "arguments": {
    "query": "SELECT category, COUNT(*) as total, AVG(price) as avg_price FROM products GROUP BY category ORDER BY total DESC"
  }
}
```

### Complex queries with subqueries
```json
{
  "name": "query",
  "arguments": {
    "query": "SELECT * FROM orders WHERE customer_id IN (SELECT id FROM customers WHERE country = 'Indonesia') ORDER BY order_date DESC LIMIT 20"
  }
}
```

## Simple Table Queries

### Query all columns from a table
```json
{
  "name": "query",
  "arguments": {
    "query": "SELECT * FROM users LIMIT 10"
  }
}
```

### Query specific columns
```json
{
  "name": "query",
  "arguments": {
    "query": "SELECT id, name, email FROM users WHERE active = true LIMIT 15"
  }
}
```

## Security Features

The tool automatically validates queries to ensure only SELECT statements are allowed:

### ✅ Allowed queries
- `SELECT * FROM table`
- `SELECT col1, col2 FROM table WHERE condition`
- `SELECT t1.*, t2.name FROM table1 t1 JOIN table2 t2 ON t1.id = t2.id`

### ❌ Blocked queries
- `INSERT INTO table VALUES (...)`
- `UPDATE table SET col = value`
- `DELETE FROM table`
- `DROP TABLE table`
- `CREATE TABLE ...`
- `ALTER TABLE ...`

## Migration from Old Tools

### Before (using separate tools with limited flexibility)
```json
// Old query_table - only SELECT * with LIMIT
{
  "name": "query_table",
  "arguments": {
    "table": "users",
    "limit": "10"
  }
}

// Old query_view - only SELECT * with LIMIT
{
  "name": "query_view",
  "arguments": {
    "view": "sales_summary",
    "limit": "5"
  }
}
```

### After (using simplified query tool with full SQL power)
```json
// Simple table query
{
  "name": "query",
  "arguments": {
    "query": "SELECT * FROM users LIMIT 10"
  }
}

// View query with specific columns and conditions
{
  "name": "query",
  "arguments": {
    "query": "SELECT product_name, total_sales FROM sales_summary WHERE month = '2024-01' ORDER BY total_sales DESC LIMIT 5"
  }
}
```

## Benefits of the Simplified Approach

1. **Maximum Flexibility**: AI agents can write any SQL SELECT query with full SQL power
2. **Better Performance**: Use specific columns, WHERE clauses, JOINs, aggregations, subqueries, etc.
3. **Simplified Interface**: One parameter (`query`) instead of multiple confusing parameters
4. **Clear and Explicit**: No ambiguity about what query will be executed
5. **Easier to Debug**: The exact SQL query is visible in the request
6. **Secure**: Built-in validation prevents dangerous SQL operations (only SELECT allowed)
7. **No Redundancy**: Table names are specified in the SQL query where they belong
