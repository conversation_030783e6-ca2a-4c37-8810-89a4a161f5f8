# Query Tool Examples

This document shows examples of how to use the new unified `query` tool that replaces the previous `query_table` and `query_view` tools.

## Direct SQL Queries (Recommended)

### Basic SELECT
```json
{
  "name": "query",
  "arguments": {
    "query": "SELECT * FROM users LIMIT 5"
  }
}
```

### SELECT with WHERE clause
```json
{
  "name": "query",
  "arguments": {
    "query": "SELECT name, email FROM users WHERE active = true ORDER BY created_at DESC LIMIT 10"
  }
}
```

### JOIN queries
```json
{
  "name": "query",
  "arguments": {
    "query": "SELECT u.name, p.title, p.created_at FROM users u JOIN posts p ON u.id = p.user_id WHERE p.published = true LIMIT 10"
  }
}
```

### Aggregate functions
```json
{
  "name": "query",
  "arguments": {
    "query": "SELECT category, COUNT(*) as total, AVG(price) as avg_price FROM products GROUP BY category ORDER BY total DESC"
  }
}
```

### Complex queries with subqueries
```json
{
  "name": "query",
  "arguments": {
    "query": "SELECT * FROM orders WHERE customer_id IN (SELECT id FROM customers WHERE country = 'Indonesia') ORDER BY order_date DESC LIMIT 20"
  }
}
```

## Backward Compatibility Mode

### Table mode (equivalent to old query_table)
```json
{
  "name": "query",
  "arguments": {
    "table": "users",
    "limit": "10"
  }
}
```

### View mode (equivalent to old query_view)
```json
{
  "name": "query",
  "arguments": {
    "view": "sales_summary",
    "limit": "15"
  }
}
```

## Security Features

The tool automatically validates queries to ensure only SELECT statements are allowed:

### ✅ Allowed queries
- `SELECT * FROM table`
- `SELECT col1, col2 FROM table WHERE condition`
- `SELECT t1.*, t2.name FROM table1 t1 JOIN table2 t2 ON t1.id = t2.id`

### ❌ Blocked queries
- `INSERT INTO table VALUES (...)`
- `UPDATE table SET col = value`
- `DELETE FROM table`
- `DROP TABLE table`
- `CREATE TABLE ...`
- `ALTER TABLE ...`

## Migration from Old Tools

### Before (using separate tools)
```json
// Old query_table
{
  "name": "query_table",
  "arguments": {
    "table": "users",
    "limit": "10"
  }
}

// Old query_view
{
  "name": "query_view",
  "arguments": {
    "view": "sales_summary",
    "limit": "5"
  }
}
```

### After (using unified query tool)
```json
// Direct SQL (recommended)
{
  "name": "query",
  "arguments": {
    "query": "SELECT * FROM users LIMIT 10"
  }
}

// Or backward compatible mode
{
  "name": "query",
  "arguments": {
    "table": "users",
    "limit": "10"
  }
}
```

## Benefits of the New Approach

1. **More Flexible**: AI agents can write complex SQL queries instead of being limited to simple table/view queries
2. **Better Performance**: Can use specific columns, WHERE clauses, JOINs, etc.
3. **Unified Interface**: One tool instead of two separate tools
4. **Backward Compatible**: Existing code using table/view parameters still works
5. **Secure**: Built-in validation prevents dangerous SQL operations
